# Ollama Code Assistant

A powerful VS Code extension that brings AI-powered coding assistance directly to your editor using local Ollama models. Get intelligent code completions, chat with AI about your code, and leverage sophisticated context understanding - all while keeping your code private on your local machine.

## 🚀 NEW: Revolutionary AI Task Agent

This extension now features a **breakthrough task-oriented AI agent** that fundamentally changes how you work with AI for coding. Unlike traditional chat interfaces that dump everything at once, our Task Agent:

- **Executes one step at a time** with clear feedback loops
- **Maintains task continuity** across multiple interactions
- **Provides actionable JSON responses** for each atomic step
- **Shows progress indicators** and next steps clearly
- **Handles complex multi-file projects** systematically

**Example Workflow:**
1. User: "Build me a React chat application"
2. Agent: Creates package.json → User executes → Agent continues
3. Agent: Installs dependencies → User executes → Agent continues
4. Agent: Creates App.js → User executes → Agent continues
5. And so on until the complete application is built!

This solves the fundamental problem with AI coding assistants: **lack of structured, step-by-step execution with proper feedback loops**.

## Features

### 🤖 Local AI Integration
- **Ollama Server Connection**: Seamlessly connects to your local Ollama instance
- **Model Management**: Discover and switch between available models
- **Privacy First**: All processing happens locally - your code never leaves your machine

### 💬 Intelligent Chat Interface
- **Context-Aware Conversations**: Chat about your code with full project context
- **Real-time Streaming**: Get responses as they're generated
- **Conversation History**: Maintain context across multiple interactions
- **Code Understanding**: Ask questions about specific code selections or entire files

### ⚡ Smart Code Completions
- **Real-time Suggestions**: Get intelligent code completions as you type
- **Multi-language Support**: Works with TypeScript, JavaScript, Python, and more
- **Context-Aware**: Understands your project structure and related files
- **Configurable**: Adjust completion delay and behavior to your preferences

### 🧠 Advanced Context Engine
- **Project Understanding**: Analyzes your entire codebase structure
- **Smart File Relations**: Identifies and includes relevant files based on imports and dependencies
- **Cursor Context**: Provides relevant code context around your current position
- **Token Management**: Efficiently manages context to fit within model limits

### 🚀 **NEW: Revolutionary AI Task Agent**
- **Multi-Step Task Execution**: Breaks complex tasks into atomic, executable steps
- **Feedback Loop Architecture**: Each step is executed and confirmed before proceeding
- **Progress Tracking**: Clear indication of current step and remaining tasks
- **JSON-Based Actions**: Structured, predictable responses for reliable automation
- **Professional Workflow**: Suitable for complex, multi-file project development

**Perfect for:**
- Building complete applications from scratch
- Setting up project structures and dependencies
- Multi-file refactoring and code organization
- Complex implementation tasks requiring multiple steps

## Requirements

- **VS Code**: Version 1.80.0 or higher
- **Ollama**: Local Ollama server running (typically on `http://localhost:11434`)
- **Models**: At least one language model installed in Ollama (e.g., `codellama`, `llama2`, `mistral`)

## Installation

### From VS Code Marketplace
1. Open VS Code
2. Go to Extensions (Ctrl+Shift+X)
3. Search for "Ollama Code Assistant"
4. Click Install

### From Source
1. Clone this repository
2. Run `npm install`
3. Run `npm run compile`
4. Press F5 to launch a new VS Code window with the extension loaded

## Setup

### 1. Install and Setup Ollama
```bash
# Install Ollama (visit https://ollama.ai for platform-specific instructions)
curl -fsSL https://ollama.ai/install.sh | sh

# Start Ollama server
ollama serve

# Install a code model (in another terminal)
ollama pull codellama
# or
ollama pull llama2
# or
ollama pull mistral
```

### 2. Configure the Extension
1. Open VS Code settings (Ctrl+,)
2. Search for "Ollama Assistant"
3. Configure your settings:
   - **Server URL**: Default is `http://localhost:11434`
   - **Default Model**: Select your preferred model for completions
   - **Chat Model**: Select your preferred model for chat (can be different from completions)

### 3. Select Your Model
1. Open the Command Palette (Ctrl+Shift+P)
2. Run "Ollama Assistant: Select Model"
3. Choose from your available models

## Usage

### 🤖 AI Task Agent (Recommended for Complex Tasks)
1. **Open Task Agent**: Click "AI Task Agent" in the sidebar or run "Ollama Assistant: Open AI Task Agent"
2. **Describe Your Task**: Enter a complex, multi-step request like:
   - "Build me a React chat application with authentication"
   - "Create a Node.js REST API with database integration"
   - "Set up a TypeScript project with testing framework"
3. **Execute Steps**: The agent will provide the first action as a JSON response
4. **Click Execute**: Review and execute each step
5. **Continue**: Click "Continue Next Task" to proceed to the next step
6. **Repeat**: Continue until your project is complete!

**Perfect for:**
- Building complete applications
- Setting up project structures
- Installing and configuring dependencies
- Multi-file refactoring tasks

### 💬 Traditional Chat Interface (For Quick Questions)
1. **Open Chat**: Click "Ollama Chat" in the sidebar or run "Ollama Assistant: Open Chat"
2. **Include Context**: Toggle "Include current file context" to send relevant code with your questions
3. **Ask Questions**:
   - "Explain this function"
   - "How can I optimize this code?"
   - "Add error handling to this function"
   - "Write unit tests for this class"

### Code Completions
- **Automatic**: Completions appear as you type (configurable delay)
- **Manual**: Press Ctrl+Space to trigger completions manually
- **Accept**: Press Tab to accept a completion
- **Dismiss**: Press Esc to dismiss suggestions

### Context Features
- **File Selection**: Select code and ask questions about specific sections
- **Project Awareness**: The AI understands your project structure and dependencies
- **Related Files**: Automatically includes relevant imported/related files in context

## Configuration

### Extension Settings

| Setting | Description | Default |
|---------|-------------|---------|
| `ollama-assistant.serverUrl` | Ollama server URL | `http://localhost:11434` |
| `ollama-assistant.defaultModel` | Default model for completions | `""` |
| `ollama-assistant.chatModel` | Model for chat conversations | `""` |
| `ollama-assistant.enableCompletions` | Enable code completions | `true` |
| `ollama-assistant.completionDelay` | Delay before triggering completions (ms) | `500` |
| `ollama-assistant.maxContextLines` | Maximum lines to include in context | `100` |

### Model Recommendations

| Use Case | Recommended Models | Notes |
|----------|-------------------|-------|
| **Code Completions** | `codellama:7b`, `codellama:13b` | Optimized for code generation |
| **Chat & Explanations** | `llama2:7b`, `mistral:7b`, `codellama:13b` | Better for conversational AI |
| **Large Projects** | `codellama:13b`, `llama2:13b` | More context understanding |
| **Fast Responses** | `codellama:7b`, `mistral:7b` | Quicker inference |

## Troubleshooting

### Common Issues

**Extension can't connect to Ollama**
- Ensure Ollama is running: `ollama serve`
- Check the server URL in settings
- Verify firewall settings

**No models available**
- Install models: `ollama pull codellama`
- Refresh models in the extension
- Check Ollama model list: `ollama list`

**Slow completions**
- Try a smaller model (7B instead of 13B)
- Increase completion delay in settings
- Check system resources (RAM, CPU)

**Poor completion quality**
- Try different models
- Adjust context settings
- Ensure relevant files are open in VS Code

### Performance Tips

1. **Model Size**: Start with 7B models for faster responses
2. **Context Management**: Reduce `maxContextLines` for faster processing
3. **Completion Delay**: Increase delay to reduce API calls
4. **System Resources**: Ensure adequate RAM (8GB+ recommended for 7B models)

## Development

### Building from Source
```bash
git clone https://github.com/yourusername/ollama-code-assistant
cd ollama-code-assistant
npm install
npm run compile
```

### Running Tests
```bash
npm test
```

### Packaging
```bash
npm run package
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

MIT License - see LICENSE file for details

## Acknowledgments

- [Ollama](https://ollama.ai) for providing the local LLM infrastructure
- [VS Code Extension API](https://code.visualstudio.com/api) for the development framework
- The open-source AI community for making local AI accessible

## Support

- **Issues**: Report bugs and feature requests on GitHub
- **Discussions**: Join the community discussions
- **Documentation**: Check the wiki for advanced usage guides

---

**Note**: This extension requires a local Ollama installation and compatible models. Performance depends on your hardware specifications and chosen model size.
