import * as vscode from 'vscode';
import * as path from 'path';
import { OllamaClient } from '../services/ollamaClient';
import { TaskAgent, TaskResponse } from '../core/augmentAgent';

export class TaskChatProvider implements vscode.TreeDataProvider<TaskChatTreeItem> {
    private _onDidChangeTreeData: vscode.EventEmitter<TaskChatTreeItem | undefined | null | void> = new vscode.EventEmitter<TaskChatTreeItem | undefined | null | void>();
    readonly onDidChangeTreeData: vscode.Event<TaskChatTreeItem | undefined | null | void> = this._onDidChangeTreeData.event;

    private chatPanel: vscode.WebviewPanel | undefined;
    private taskAgent: TaskAgent;
    private isProcessingTask = false;

    constructor(
        private context: vscode.ExtensionContext,
        private ollamaClient: OllamaClient
    ) {
        this.taskAgent = new TaskAgent(ollamaClient, context);
    }

    getTreeItem(element: TaskChatTreeItem): vscode.TreeItem {
        return element;
    }

    getChildren(element?: TaskChatTreeItem): Thenable<TaskChatTreeItem[]> {
        if (!element) {
            return Promise.resolve([
                new TaskChatTreeItem('New Task Chat', vscode.TreeItemCollapsibleState.None, 'openTaskChat'),
                new TaskChatTreeItem('Clear Task History', vscode.TreeItemCollapsibleState.None, 'clearTaskHistory')
            ]);
        }
        return Promise.resolve([]);
    }

    getTreeDataProvider(): vscode.TreeDataProvider<TaskChatTreeItem> {
        return this;
    }

    async openTaskChat(): Promise<void> {
        if (this.chatPanel) {
            this.chatPanel.reveal(vscode.ViewColumn.Beside);
            return;
        }

        this.chatPanel = vscode.window.createWebviewPanel(
            'task-chat',
            'AI Task Agent',
            vscode.ViewColumn.Beside,
            {
                enableScripts: true,
                retainContextWhenHidden: true,
                localResourceRoots: [
                    vscode.Uri.joinPath(this.context.extensionUri, 'media'),
                    vscode.Uri.joinPath(this.context.extensionUri, 'out')
                ]
            }
        );

        this.chatPanel.webview.html = this.getWebviewContent();

        // Handle messages from webview
        this.chatPanel.webview.onDidReceiveMessage(
            async (message) => {
                switch (message.type) {
                    case 'startTask':
                        await this.handleStartTask(message.text, message.includeContext);
                        break;
                    case 'continueTask':
                        await this.handleContinueTask();
                        break;
                    case 'executeAction':
                        await this.handleExecuteAction(message.taskResponse);
                        break;
                    case 'clearTaskHistory':
                        await this.handleClearTaskHistory();
                        break;
                    case 'getModels':
                        await this.handleGetModels();
                        break;
                    case 'selectModel':
                        await this.handleSelectModel(message.model);
                        break;
                }
            },
            undefined,
            this.context.subscriptions
        );

        this.chatPanel.onDidDispose(() => {
            this.chatPanel = undefined;
        });

        // Send initial data
        await this.sendInitialData();
    }

    private async sendInitialData(): Promise<void> {
        if (!this.chatPanel) {
            return;
        }

        // Send task history
        this.chatPanel.webview.postMessage({
            type: 'taskHistory',
            history: this.taskAgent.getTaskHistory()
        });

        // Send current task status
        this.chatPanel.webview.postMessage({
            type: 'taskStatus',
            hasNextTask: this.taskAgent.hasNextTask(),
            currentTask: this.taskAgent.getCurrentTask()
        });

        // Send available models
        try {
            const models = this.ollamaClient.getAvailableModels();
            this.chatPanel.webview.postMessage({
                type: 'availableModels',
                models: models.map(m => m.name)
            });
        } catch (error) {
            console.error('Error getting models:', error);
        }
    }

    private async handleStartTask(text: string, includeContext: boolean): Promise<void> {
        if (!this.chatPanel || !text.trim() || this.isProcessingTask) {
            return;
        }

        this.isProcessingTask = true;

        // Send user message to webview
        this.chatPanel.webview.postMessage({
            type: 'userMessage',
            message: text
        });

        // Show processing indicator
        this.chatPanel.webview.postMessage({
            type: 'taskProcessingStart'
        });

        try {
            // Get context if requested
            const activeEditor = vscode.window.activeTextEditor;
            
            // Process the task
            const taskResponse = await this.taskAgent.processTask(
                text,
                includeContext,
                activeEditor?.document,
                activeEditor?.selection
            );

            // Send task response to webview
            this.chatPanel.webview.postMessage({
                type: 'taskResponse',
                response: taskResponse
            });

            // Update task status
            this.chatPanel.webview.postMessage({
                type: 'taskStatus',
                hasNextTask: this.taskAgent.hasNextTask(),
                currentTask: this.taskAgent.getCurrentTask()
            });

        } catch (error) {
            console.error('Error processing task:', error);
            this.chatPanel.webview.postMessage({
                type: 'error',
                message: `Error: ${error}`
            });
        } finally {
            this.isProcessingTask = false;
            this.chatPanel.webview.postMessage({
                type: 'taskProcessingEnd'
            });
        }
    }

    private async handleContinueTask(): Promise<void> {
        if (!this.chatPanel || this.isProcessingTask || !this.taskAgent.hasNextTask()) {
            return;
        }

        this.isProcessingTask = true;

        // Show processing indicator
        this.chatPanel.webview.postMessage({
            type: 'taskProcessingStart'
        });

        try {
            const taskResponse = await this.taskAgent.processNextTask();
            
            if (taskResponse) {
                // Send task response to webview
                this.chatPanel.webview.postMessage({
                    type: 'taskResponse',
                    response: taskResponse
                });

                // Update task status
                this.chatPanel.webview.postMessage({
                    type: 'taskStatus',
                    hasNextTask: this.taskAgent.hasNextTask(),
                    currentTask: this.taskAgent.getCurrentTask()
                });
            }

        } catch (error) {
            console.error('Error continuing task:', error);
            this.chatPanel.webview.postMessage({
                type: 'error',
                message: `Error: ${error}`
            });
        } finally {
            this.isProcessingTask = false;
            this.chatPanel.webview.postMessage({
                type: 'taskProcessingEnd'
            });
        }
    }

    private async handleExecuteAction(taskResponse: TaskResponse): Promise<void> {
        try {
            switch (taskResponse.action) {
                case 'create_file':
                    await this.executeCreateFile(taskResponse);
                    break;
                case 'modify_file':
                    await this.executeModifyFile(taskResponse);
                    break;
                case 'delete_file':
                    await this.executeDeleteFile(taskResponse);
                    break;
                case 'run_command':
                    await this.executeRunCommand(taskResponse);
                    break;
                case 'chat':
                    // No action needed for chat responses
                    break;
            }

            // Send execution success message
            if (this.chatPanel) {
                this.chatPanel.webview.postMessage({
                    type: 'actionExecuted',
                    success: true,
                    message: `Successfully executed: ${taskResponse.task_description}`
                });
            }

        } catch (error) {
            console.error('Error executing action:', error);
            if (this.chatPanel) {
                this.chatPanel.webview.postMessage({
                    type: 'actionExecuted',
                    success: false,
                    message: `Failed to execute action: ${error}`
                });
            }
        }
    }

    private async executeCreateFile(taskResponse: TaskResponse): Promise<void> {
        if (!taskResponse.file_path || !taskResponse.content) {
            throw new Error('Missing file path or content for create_file action');
        }

        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            throw new Error('No workspace folder open');
        }

        const filePath = vscode.Uri.joinPath(workspaceFolder.uri, taskResponse.file_path);
        await vscode.workspace.fs.writeFile(filePath, Buffer.from(taskResponse.content, 'utf8'));
        
        // Open the created file
        const document = await vscode.workspace.openTextDocument(filePath);
        await vscode.window.showTextDocument(document);
    }

    private async executeModifyFile(taskResponse: TaskResponse): Promise<void> {
        if (!taskResponse.file_path || !taskResponse.content) {
            throw new Error('Missing file path or content for modify_file action');
        }

        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            throw new Error('No workspace folder open');
        }

        const filePath = vscode.Uri.joinPath(workspaceFolder.uri, taskResponse.file_path);
        await vscode.workspace.fs.writeFile(filePath, Buffer.from(taskResponse.content, 'utf8'));
        
        // Open the modified file
        const document = await vscode.workspace.openTextDocument(filePath);
        await vscode.window.showTextDocument(document);
    }

    private async executeDeleteFile(taskResponse: TaskResponse): Promise<void> {
        if (!taskResponse.file_path) {
            throw new Error('Missing file path for delete_file action');
        }

        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            throw new Error('No workspace folder open');
        }

        const filePath = vscode.Uri.joinPath(workspaceFolder.uri, taskResponse.file_path);
        await vscode.workspace.fs.delete(filePath);
    }

    private async executeRunCommand(taskResponse: TaskResponse): Promise<void> {
        if (!taskResponse.command) {
            throw new Error('Missing command for run_command action');
        }

        // Create a new terminal and run the command
        const terminal = vscode.window.createTerminal('AI Task Agent');
        terminal.sendText(taskResponse.command);
        terminal.show();
    }

    private async handleClearTaskHistory(): Promise<void> {
        this.taskAgent.clearTaskHistory();
        if (this.chatPanel) {
            this.chatPanel.webview.postMessage({
                type: 'taskHistoryCleared'
            });
            this.chatPanel.webview.postMessage({
                type: 'taskStatus',
                hasNextTask: false,
                currentTask: null
            });
        }
    }

    private async handleGetModels(): Promise<void> {
        try {
            await this.ollamaClient.refreshModels();
            const models = this.ollamaClient.getAvailableModels();
            if (this.chatPanel) {
                this.chatPanel.webview.postMessage({
                    type: 'availableModels',
                    models: models.map(m => m.name)
                });
            }
        } catch (error) {
            console.error('Error getting models:', error);
            if (this.chatPanel) {
                this.chatPanel.webview.postMessage({
                    type: 'error',
                    message: 'Failed to load models'
                });
            }
        }
    }

    private async handleSelectModel(model: string): Promise<void> {
        try {
            await vscode.workspace.getConfiguration('ollama-assistant').update('chatModel', model, vscode.ConfigurationTarget.Global);
            if (this.chatPanel) {
                this.chatPanel.webview.postMessage({
                    type: 'modelSelected',
                    model
                });
            }
        } catch (error) {
            console.error('Error selecting model:', error);
        }
    }

    private getWebviewContent(): string {
        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Task Agent</title>
    <style>
        body {
            font-family: var(--vscode-font-family);
            font-size: var(--vscode-font-size);
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
            margin: 0;
            padding: 20px;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid var(--vscode-panel-border);
        }

        .header-title {
            font-size: 1.2em;
            font-weight: bold;
            color: var(--vscode-textLink-foreground);
        }

        .model-selector {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            gap: 10px;
        }

        .task-status {
            background-color: var(--vscode-badge-background);
            color: var(--vscode-badge-foreground);
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .task-status.has-next {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
        }

        select {
            background-color: var(--vscode-dropdown-background);
            color: var(--vscode-dropdown-foreground);
            border: 1px solid var(--vscode-dropdown-border);
            padding: 8px;
            border-radius: 3px;
            min-width: 200px;
        }

        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .messages {
            flex: 1;
            overflow-y: auto;
            padding: 10px;
            border: 1px solid var(--vscode-panel-border);
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .message {
            margin-bottom: 15px;
            padding: 12px;
            border-radius: 6px;
        }

        .user-message {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            margin-left: 20%;
        }

        .task-response {
            background-color: var(--vscode-editor-selectionBackground);
            margin-right: 20%;
            border-left: 4px solid var(--vscode-textLink-foreground);
        }

        .task-header {
            font-weight: bold;
            margin-bottom: 8px;
            color: var(--vscode-textLink-foreground);
        }

        .task-description {
            margin-bottom: 10px;
            font-style: italic;
        }

        .task-action {
            background-color: var(--vscode-textCodeBlock-background);
            padding: 10px;
            border-radius: 4px;
            margin: 8px 0;
            font-family: var(--vscode-editor-font-family);
        }

        .task-actions {
            display: flex;
            gap: 8px;
            margin-top: 10px;
        }

        .next-task {
            background-color: var(--vscode-inputValidation-infoBackground);
            border: 1px solid var(--vscode-inputValidation-infoBorder);
            padding: 8px;
            border-radius: 4px;
            margin-top: 8px;
            font-size: 12px;
        }

        button {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-family: inherit;
            font-size: 13px;
        }

        button:hover {
            background-color: var(--vscode-button-hoverBackground);
        }

        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .execute-btn {
            background-color: var(--vscode-button-secondaryBackground);
            color: var(--vscode-button-secondaryForeground);
        }

        .execute-btn:hover {
            background-color: var(--vscode-button-secondaryHoverBackground);
        }

        .continue-btn {
            background-color: var(--vscode-textLink-foreground);
            color: var(--vscode-editor-background);
        }

        .input-container {
            display: flex;
            flex-direction: column;
            gap: 10px;
            padding: 10px;
            border-top: 1px solid var(--vscode-panel-border);
            background-color: var(--vscode-editor-background);
        }

        textarea {
            width: 100%;
            min-height: 80px;
            max-height: 150px;
            padding: 12px;
            border: 1px solid var(--vscode-input-border);
            border-radius: 5px;
            background-color: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            font-family: inherit;
            font-size: inherit;
            resize: vertical;
            box-sizing: border-box;
        }

        .input-options {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 10px;
        }

        .processing {
            color: var(--vscode-descriptionForeground);
            font-style: italic;
            text-align: center;
            padding: 20px;
        }

        .error {
            color: var(--vscode-errorForeground);
            background-color: var(--vscode-inputValidation-errorBackground);
            border: 1px solid var(--vscode-inputValidation-errorBorder);
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 10px;
        }

        .success {
            color: var(--vscode-terminal-ansiGreen);
            background-color: var(--vscode-inputValidation-infoBackground);
            border: 1px solid var(--vscode-inputValidation-infoBorder);
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 10px;
        }

        pre {
            background-color: var(--vscode-textCodeBlock-background);
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            white-space: pre-wrap;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-title">🤖 AI Task Agent</div>
        <div class="model-selector">
            <label for="model-select">Model:</label>
            <select id="model-select">
                <option value="">Select a model...</option>
            </select>
            <button onclick="refreshModels()">Refresh</button>
            <button onclick="clearTaskHistory()">Clear History</button>
        </div>
        <div id="task-status" class="task-status">No active task</div>
    </div>

    <div class="chat-container">
        <div id="messages" class="messages"></div>

        <div class="input-container">
            <div class="input-options">
                <label>
                    <input type="checkbox" id="include-context" checked>
                    Include current file context
                </label>
                <button id="continue-btn" onclick="continueTask()" disabled class="continue-btn">
                    Continue Next Task
                </button>
            </div>
            <textarea id="message-input" placeholder="Describe the task you want me to complete..."></textarea>
            <button onclick="startTask()" id="start-button">Start Task</button>
        </div>
    </div>

    <script>
        const vscode = acquireVsCodeApi();
        let isProcessing = false;

        // Handle messages from extension
        window.addEventListener('message', event => {
            const message = event.data;
            console.log('Webview received message:', message);

            switch (message.type) {
                case 'taskHistory':
                    displayTaskHistory(message.history);
                    break;
                case 'taskStatus':
                    updateTaskStatus(message.hasNextTask, message.currentTask);
                    break;
                case 'availableModels':
                    updateModelSelector(message.models);
                    break;
                case 'userMessage':
                    displayUserMessage(message.message);
                    break;
                case 'taskResponse':
                    displayTaskResponse(message.response);
                    break;
                case 'taskProcessingStart':
                    showProcessing(true);
                    break;
                case 'taskProcessingEnd':
                    showProcessing(false);
                    break;
                case 'actionExecuted':
                    showActionResult(message.success, message.message);
                    break;
                case 'error':
                    displayError(message.message);
                    break;
                case 'taskHistoryCleared':
                    clearMessages();
                    break;
                case 'modelSelected':
                    document.getElementById('model-select').value = message.model;
                    break;
            }
        });

        function startTask() {
            const input = document.getElementById('message-input');
            const includeContext = document.getElementById('include-context').checked;
            const text = input.value.trim();

            if (!text || isProcessing) return;

            vscode.postMessage({
                type: 'startTask',
                text: text,
                includeContext: includeContext
            });

            input.value = '';
        }

        function continueTask() {
            if (isProcessing) return;

            vscode.postMessage({
                type: 'continueTask'
            });
        }

        function executeAction(taskResponse) {
            vscode.postMessage({
                type: 'executeAction',
                taskResponse: taskResponse
            });
        }

        function clearTaskHistory() {
            vscode.postMessage({ type: 'clearTaskHistory' });
        }

        function refreshModels() {
            vscode.postMessage({ type: 'getModels' });
        }

        function displayTaskHistory(history) {
            const messagesDiv = document.getElementById('messages');
            messagesDiv.innerHTML = '';

            history.forEach(item => {
                displayUserMessage(item.task);
                displayTaskResponse(item.response);
            });
        }

        function displayUserMessage(message) {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message user-message';
            messageDiv.textContent = message;
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        function displayTaskResponse(response) {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message task-response';

            let content = '<div class="task-header">🎯 Task Response</div>';
            content += '<div class="task-description">' + response.task_description + '</div>';

            // Show the action details
            content += '<div class="task-action">';
            content += '<strong>Action:</strong> ' + response.action + '<br>';

            if (response.file_path) {
                content += '<strong>File:</strong> ' + response.file_path + '<br>';
            }
            if (response.command) {
                content += '<strong>Command:</strong> <code>' + response.command + '</code><br>';
            }
            if (response.message) {
                content += '<strong>Message:</strong> ' + response.message + '<br>';
            }
            if (response.content) {
                content += '<strong>Content:</strong><pre>' + escapeHtml(response.content.substring(0, 200)) +
                          (response.content.length > 200 ? '...' : '') + '</pre>';
            }
            content += '</div>';

            // Add action buttons
            if (response.action !== 'chat') {
                content += '<div class="task-actions">';
                content += '<button class="execute-btn" onclick="executeAction(' + JSON.stringify(response).replace(/"/g, '&quot;') + ')">Execute</button>';
                content += '</div>';
            }

            // Show next task if available
            if (response.next_task) {
                content += '<div class="next-task"><strong>Next:</strong> ' + response.next_task + '</div>';
            }

            messageDiv.innerHTML = content;
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        function updateTaskStatus(hasNextTask, currentTask) {
            const statusDiv = document.getElementById('task-status');
            const continueBtn = document.getElementById('continue-btn');

            if (hasNextTask && currentTask) {
                statusDiv.textContent = 'Next: ' + currentTask;
                statusDiv.className = 'task-status has-next';
                continueBtn.disabled = false;
            } else {
                statusDiv.textContent = 'No active task';
                statusDiv.className = 'task-status';
                continueBtn.disabled = true;
            }
        }

        function showProcessing(processing) {
            isProcessing = processing;
            const startButton = document.getElementById('start-button');
            const continueBtn = document.getElementById('continue-btn');

            startButton.disabled = processing;
            if (!processing) {
                // Re-enable continue button based on task status
                const hasNext = !continueBtn.disabled;
                continueBtn.disabled = !hasNext;
            } else {
                continueBtn.disabled = true;
            }

            if (processing) {
                const messagesDiv = document.getElementById('messages');
                const processingDiv = document.createElement('div');
                processingDiv.className = 'processing';
                processingDiv.id = 'processing-indicator';
                processingDiv.textContent = '🤔 Processing task...';
                messagesDiv.appendChild(processingDiv);
                messagesDiv.scrollTop = messagesDiv.scrollHeight;
            } else {
                const processingDiv = document.getElementById('processing-indicator');
                if (processingDiv) {
                    processingDiv.remove();
                }
            }
        }

        function showActionResult(success, message) {
            const messagesDiv = document.getElementById('messages');
            const resultDiv = document.createElement('div');
            resultDiv.className = success ? 'success' : 'error';
            resultDiv.textContent = message;
            messagesDiv.appendChild(resultDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        function displayError(message) {
            const messagesDiv = document.getElementById('messages');
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error';
            errorDiv.textContent = message;
            messagesDiv.appendChild(errorDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        function clearMessages() {
            document.getElementById('messages').innerHTML = '';
        }

        function updateModelSelector(models) {
            const select = document.getElementById('model-select');
            const currentValue = select.value;

            select.innerHTML = '<option value="">Select a model...</option>';
            models.forEach(model => {
                const option = document.createElement('option');
                option.value = model;
                option.textContent = model;
                select.appendChild(option);
            });

            if (models.includes(currentValue)) {
                select.value = currentValue;
            }
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // Handle Enter key in textarea
        document.getElementById('message-input').addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                startTask();
            }
        });

        // Handle model selection
        document.getElementById('model-select').addEventListener('change', function(e) {
            if (e.target.value) {
                vscode.postMessage({
                    type: 'selectModel',
                    model: e.target.value
                });
            }
        });

        // Request initial data
        vscode.postMessage({ type: 'getModels' });
    </script>
</body>
</html>`;
    }

    dispose(): void {
        if (this.chatPanel) {
            this.chatPanel.dispose();
        }
    }
}

class TaskChatTreeItem extends vscode.TreeItem {
    constructor(
        public readonly label: string,
        public readonly collapsibleState: vscode.TreeItemCollapsibleState,
        public readonly commandId?: string
    ) {
        super(label, collapsibleState);

        if (commandId) {
            this.command = {
                command: `ollama-assistant.${commandId}`,
                title: label
            };
        }
    }
}
