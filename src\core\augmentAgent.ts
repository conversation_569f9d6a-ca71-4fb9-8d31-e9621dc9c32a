import * as vscode from 'vscode';
import { OllamaClient } from '../services/ollamaClient';
import { AdvancedContextEngine } from './contextEngine';
import { MemorySystem } from './memorySystem';
import { CodeCheckpoints } from './codeCheckpoints';
import { ToolRegistry } from './toolRegistry';

export interface AgentCapabilities {
    chat: boolean;
    nextEdit: boolean;
    completions: boolean;
    codeGeneration: boolean;
    fileOperations: boolean;
    terminalCommands: boolean;
    multiModal: boolean;
}

export interface AgentMemory {
    id: string;
    timestamp: Date;
    context: string;
    codeStyle: any;
    patterns: string[];
    preferences: any;
}

export interface AgentResponse {
    content: string;
    actions: AgentAction[];
    nextEdits?: NextEdit[];
    memories?: AgentMemory[];
    sources?: string[];
}

export interface TaskResponse {
    action: 'create_file' | 'modify_file' | 'delete_file' | 'run_command' | 'chat' | 'completed';
    file_path?: string;
    content?: string;
    command?: string;
    message?: string;
    task_description: string;
    next_task: string | null;
}

export interface AgentAction {
    type: 'create_file' | 'modify_file' | 'delete_file' | 'run_command' | 'apply_code' | 'next_edit';
    target: string;
    content?: string;
    description: string;
    confidence: number;
}

export interface NextEdit {
    id: string;
    file: string;
    line: number;
    column: number;
    type: 'insert' | 'replace' | 'delete';
    content: string;
    description: string;
    dependencies: string[];
}

export class TaskAgent {
    private contextEngine: AdvancedContextEngine;
    private currentTask: string | null = null;
    private taskHistory: Array<{task: string, response: TaskResponse}> = [];

    constructor(
        private ollamaClient: OllamaClient,
        private context: vscode.ExtensionContext
    ) {
        this.contextEngine = new AdvancedContextEngine();
    }

    async processTask(
        userRequest: string,
        includeContext: boolean = true,
        activeFile?: vscode.TextDocument,
        selection?: vscode.Selection
    ): Promise<TaskResponse> {
        try {
            // Gather comprehensive context
            const contextBundle = includeContext ?
                await this.contextEngine.gatherComprehensiveContext(activeFile, selection) :
                null;

            // Build the task-oriented prompt
            const prompt = this.buildTaskPrompt(userRequest, contextBundle);

            // Get AI response
            const model = this.getSelectedModel();
            let fullResponse = '';

            for await (const chunk of this.ollamaClient.chatStream({
                model,
                messages: [{ role: 'user', content: prompt }],
                options: this.getOptimizedOptions()
            })) {
                if (chunk.message?.content) {
                    fullResponse += chunk.message.content;
                }
            }

            // Parse the JSON response
            const taskResponse = this.parseTaskResponse(fullResponse);

            // Store current task for follow-up
            this.currentTask = taskResponse.next_task;
            this.taskHistory.push({ task: userRequest, response: taskResponse });

            return taskResponse;

        } catch (error) {
            console.error('Task processing error:', error);
            return {
                action: 'chat',
                message: `Error processing task: ${error}`,
                task_description: 'Error occurred',
                next_task: null
            };
        }
    }

    async processNextTask(): Promise<TaskResponse | null> {
        if (!this.currentTask) {
            return null;
        }

        const followUpPrompt = this.buildFollowUpPrompt(this.currentTask);

        try {
            const model = this.getSelectedModel();
            let fullResponse = '';

            for await (const chunk of this.ollamaClient.chatStream({
                model,
                messages: [{ role: 'user', content: followUpPrompt }],
                options: this.getOptimizedOptions()
            })) {
                if (chunk.message?.content) {
                    fullResponse += chunk.message.content;
                }
            }

            const taskResponse = this.parseTaskResponse(fullResponse);

            // Update current task
            this.currentTask = taskResponse.next_task;
            this.taskHistory.push({ task: this.currentTask || 'follow-up', response: taskResponse });

            return taskResponse;

        } catch (error) {
            console.error('Next task processing error:', error);
            return {
                action: 'chat',
                message: `Error processing next task: ${error}`,
                task_description: 'Error occurred',
                next_task: null
            };
        }
    }

    private buildTaskPrompt(userRequest: string, contextBundle: any): string {
        let prompt = `You are a highly capable AI agent for a VS Code extension. Your goal is to complete complex programming tasks by breaking them down into small, single, atomic commands. For every task, you will provide a single JSON response that contains the **one action to be executed now** and a brief description of the **next logical task to complete the overall goal.**

**Task-Oriented Response Format:**

Your response **must be a single JSON object only**. It should have the following structure:

{
  "action": "The command for me to execute now (e.g., create_file, modify_file, run_command).",
  "file_path": "Relevant file path (for file actions).",
  "content": "Full content of the file (for file actions).",
  "command": "The shell command to run (for run_command).",
  "task_description": "A brief, human-readable summary of what you just did.",
  "next_task": "A brief, human-readable description of the next task required to complete the user's request, or null if the entire task is complete."
}

**CRITICAL RULES:**
1. Your response must contain ONLY a valid JSON object - no preambles, explanations, or follow-up text
2. Start your response with { and end with }
3. For file operations, provide the COMPLETE file content, not just snippets
4. For new projects requiring dependencies, your first action must be run_command with npm install
5. Break complex tasks into small, atomic steps
6. Only provide the FIRST step - wait for confirmation before proceeding

`;

        if (contextBundle) {
            prompt += `\n**CURRENT WORKSPACE CONTEXT:**\n`;
            if (contextBundle.workspace) {
                prompt += `- Root: ${contextBundle.workspace.rootPath}\n`;
                prompt += `- Files: ${contextBundle.workspace.fileCount} files\n`;
                prompt += `- Languages: ${contextBundle.workspace.languages.join(', ')}\n`;
            }

            if (contextBundle.activeFile) {
                prompt += `\n**ACTIVE FILE:**\n`;
                prompt += `- Path: ${contextBundle.activeFile.path}\n`;
                prompt += `- Language: ${contextBundle.activeFile.language}\n`;

                if (contextBundle.activeFile.content) {
                    prompt += `- Content:\n\`\`\`${contextBundle.activeFile.language}\n${contextBundle.activeFile.content}\n\`\`\`\n`;
                }
            }

            if (contextBundle.dependencies?.length > 0) {
                prompt += `\n**EXISTING DEPENDENCIES:**\n`;
                contextBundle.dependencies.forEach((dep: any) => {
                    prompt += `- ${dep.name}@${dep.version}\n`;
                });
            }
        }

        prompt += `\n**USER REQUEST:** ${userRequest}\n\n`;
        prompt += `Respond with a single JSON object for the FIRST action to take:`;

        return prompt;
    }

    private buildFollowUpPrompt(nextTask: string): string {
        const lastTask = this.taskHistory[this.taskHistory.length - 1];

        return `The previous task "${lastTask.task}" is complete. The task description was: "${lastTask.response.task_description}".

The next task is: "${nextTask}"

Please provide the JSON for this next step. Remember:
- Your response must be a single JSON object only
- No preambles, explanations, or follow-up text
- Start with { and end with }
- Provide complete file content for file operations

Respond with the JSON for the next action:`;
    }

    private parseTaskResponse(response: string): TaskResponse {
        try {
            // Clean the response - remove any non-JSON content
            const cleanedResponse = response.trim();

            // Try to find JSON in the response
            const jsonMatch = cleanedResponse.match(/\{[\s\S]*\}/);
            if (!jsonMatch) {
                throw new Error('No JSON found in response');
            }

            const jsonResponse = JSON.parse(jsonMatch[0]);

            // Validate required fields
            if (!jsonResponse.action || !jsonResponse.task_description) {
                throw new Error('Invalid JSON structure - missing required fields');
            }

            return {
                action: jsonResponse.action,
                file_path: jsonResponse.file_path,
                content: jsonResponse.content,
                command: jsonResponse.command,
                message: jsonResponse.message,
                task_description: jsonResponse.task_description,
                next_task: jsonResponse.next_task
            };

        } catch (error) {
            console.error('Failed to parse task response:', error);
            console.error('Raw response:', response);

            // Return a fallback response
            return {
                action: 'chat',
                message: `I encountered an error parsing the response. Raw response: ${response}`,
                task_description: 'Error parsing response',
                next_task: null
            };
        }
    }

    private getSelectedModel(): string {
        return vscode.workspace.getConfiguration('ollama-assistant').get('chatModel') || 'llama2';
    }

    private getOptimizedOptions(): any {
        return {
            temperature: 0.1,
            top_p: 0.9,
            num_ctx: 8192
        };
    }

    hasNextTask(): boolean {
        return this.currentTask !== null;
    }

    getCurrentTask(): string | null {
        return this.currentTask;
    }

    getTaskHistory(): Array<{task: string, response: TaskResponse}> {
        return [...this.taskHistory];
    }

    clearTaskHistory(): void {
        this.currentTask = null;
        this.taskHistory = [];
    }
}

export class AugmentAgent {
    private capabilities: AgentCapabilities;
    private memorySystem: MemorySystem;
    private contextEngine: AdvancedContextEngine;
    private checkpoints: CodeCheckpoints;
    private toolRegistry: ToolRegistry;
    private conversationHistory: any[] = [];

    constructor(
        private ollamaClient: OllamaClient,
        private context: vscode.ExtensionContext
    ) {
        this.capabilities = {
            chat: true,
            nextEdit: true,
            completions: true,
            codeGeneration: true,
            fileOperations: true,
            terminalCommands: true,
            multiModal: false // Will be implemented later
        };

        this.memorySystem = new MemorySystem(context);
        this.contextEngine = new AdvancedContextEngine();
        this.checkpoints = new CodeCheckpoints(context);
        this.toolRegistry = new ToolRegistry();

        this.initializeTools();
    }

    private initializeTools(): void {
        // Register built-in tools
        this.toolRegistry.register('create_file', this.createFile.bind(this));
        this.toolRegistry.register('modify_file', this.modifyFile.bind(this));
        this.toolRegistry.register('delete_file', this.deleteFile.bind(this));
        this.toolRegistry.register('run_command', this.runCommand.bind(this));
        this.toolRegistry.register('apply_code', this.applyCode.bind(this));
        this.toolRegistry.register('get_context', this.getContext.bind(this));
    }

    async processRequest(
        message: string, 
        includeContext: boolean = true,
        activeFile?: vscode.TextDocument,
        selection?: vscode.Selection
    ): Promise<AgentResponse> {
        try {
            // Create checkpoint before making changes
            const checkpointId = await this.checkpoints.create('Before agent action');

            // Gather comprehensive context
            const contextBundle = includeContext ? 
                await this.contextEngine.gatherComprehensiveContext(activeFile, selection) : 
                null;

            // Retrieve relevant memories
            const relevantMemories = await this.memorySystem.getRelevantMemories(message, contextBundle);

            // Build enhanced system prompt
            const systemPrompt = this.buildEnhancedSystemPrompt(contextBundle, relevantMemories);

            // Build structured context for the user message
            const contextualMessage = this.buildContextualMessage(message, contextBundle);

            // Prepare conversation with context
            const messages = [
                { role: 'system', content: systemPrompt },
                ...this.conversationHistory,
                { role: 'user', content: contextualMessage }
            ];

            // Get AI response
            let fullResponse = '';
            for await (const chunk of this.ollamaClient.chatStream({
                model: this.getSelectedModel(),
                messages,
                options: this.getOptimizedOptions()
            })) {
                if (chunk.message?.content) {
                    fullResponse += chunk.message.content;
                }
            }

            // Parse response and extract actions
            const parsedResponse = this.parseAgentResponse(fullResponse);

            // Execute actions if any
            if (parsedResponse.actions.length > 0) {
                await this.executeActions(parsedResponse.actions);
            }

            // Update memories
            await this.memorySystem.updateMemories(message, parsedResponse, contextBundle);

            // Add to conversation history
            this.conversationHistory.push(
                { role: 'user', content: message },
                { role: 'assistant', content: fullResponse }
            );

            return parsedResponse;

        } catch (error) {
            console.error('Agent processing error:', error);
            throw error;
        }
    }

    private buildEnhancedSystemPrompt(contextBundle: any, memories: AgentMemory[]): string {
        let prompt = `[SYSTEM INSTRUCTIONS]
You are **Augment Agent**, an expert software engineering AI assistant. Your primary goal is to act as a highly proficient and proactive pair programmer. You are not a simple chatbot. You understand complex codebases, architecture, best practices, and project structure. You are capable of multi-step problem solving and breaking down large tasks into manageable, executable actions. You will operate in an **Agent** mode by default, unless explicitly asked to switch to **Chat** mode. Your responses for Agent mode will be in the form of a structured JSON command.

For every user request, you will follow this exact, multi-step process. **Do not skip any steps.**

**Step 1: ANALYSIS & UNDERSTANDING.**
* Analyze the user's request and the provided codebase context.
* Identify all necessary files, dependencies, and architectural changes required.
* Formulate a high-level plan to achieve the user's goal.

**Step 2: PLANNING & EXECUTION.**
* Break down the high-level plan into a series of small, atomic, and actionable agent commands.
* Identify the very first action to take. This could be creating a file, modifying a file, or installing a dependency.
* Generate a JSON object for **only the first command.** You will wait for me to confirm or execute this before proceeding to the next step.

**Step 3: DEPENDENCY & ENVIRONMENT SETUP.**
* Before writing any code that requires external libraries (e.g., React, Express, etc.), your first action must be to create a command to install the necessary dependencies (npm install). This is a non-negotiable step for any new project or feature.

**ACTION CHECKLIST:**
Before you output any code, ask yourself these questions and incorporate the answers into your final JSON response:
* Does this task require new files? If yes, identify the correct directory and file name.
* Does this task require new dependencies? If yes, your first action must be to generate a JSON to run npm install [package-name].
* Does this code require imports? If yes, ensure all necessary imports are at the top of the file.
* What is the full content of the file? When modifying a file, do not just provide the new function. Provide the *entire* file content, including existing code, so the extension can replace the old content completely.

**FINAL RULE: JSON OUTPUT ONLY.**
Your response **must contain nothing but a single JSON object**. Do not include any preambles, explanations, internal monologues, or follow-up text. Just the raw JSON.

**JSON FORMATS:**
File creation: {"action": "create_file", "file_path": "filename.js", "content": "complete file content"}
File modification: {"action": "modify_file", "file_path": "filename.js", "content": "complete file content"}
File deletion: {"action": "delete_file", "file_path": "filename.js"}
Command execution: {"action": "run_command", "command": "npm install package-name"}
Chat response: {"action": "chat", "message": "response here"}`;

        if (contextBundle) {
            prompt += `\n\nCURRENT WORKSPACE:`;
            if (contextBundle.workspace) {
                prompt += `\n- Root: ${contextBundle.workspace.rootPath}`;
                prompt += `\n- Files: ${contextBundle.workspace.fileCount} files`;
                prompt += `\n- Languages: ${contextBundle.workspace.languages.join(', ')}`;
            }

            if (contextBundle.activeFile) {
                prompt += `\n\nACTIVE FILE:`;
                prompt += `\n- Path: ${contextBundle.activeFile.path}`;
                prompt += `\n- Language: ${contextBundle.activeFile.language}`;
                prompt += `\n- Lines: ${contextBundle.activeFile.lineCount}`;
                
                if (contextBundle.activeFile.selection) {
                    prompt += `\n- Selected: ${contextBundle.activeFile.selection.text}`;
                }
            }

            if (contextBundle.relatedFiles?.length > 0) {
                prompt += `\n\nRELATED FILES:`;
                contextBundle.relatedFiles.forEach((file: any) => {
                    prompt += `\n- ${file.path} (${file.relationship})`;
                });
            }

            if (contextBundle.dependencies?.length > 0) {
                prompt += `\n\nDEPENDENCIES:`;
                contextBundle.dependencies.forEach((dep: any) => {
                    prompt += `\n- ${dep.name}@${dep.version}`;
                });
            }
        }

        if (memories.length > 0) {
            prompt += `\n\nRELEVANT MEMORIES:`;
            memories.forEach(memory => {
                prompt += `\n- ${memory.context}`;
                if (memory.patterns.length > 0) {
                    prompt += ` (Patterns: ${memory.patterns.join(', ')})`;
                }
            });
        }

        prompt += `\n\nRemember: Provide clear, actionable responses with proper code formatting and specific file operations.`;

        return prompt;
    }

    private buildContextualMessage(userMessage: string, contextBundle: any): string {
        let contextualMessage = '';

        if (contextBundle) {
            contextualMessage += '[CONTEXT]\n';

            // Active File Information
            if (contextBundle.activeFile) {
                contextualMessage += `Active File:\n${contextBundle.activeFile.path}\n`;

                if (contextBundle.activeFile.content) {
                    contextualMessage += `${contextBundle.activeFile.content}\n\n`;
                }

                if (contextBundle.activeFile.selection) {
                    contextualMessage += `Selected code:\n${contextBundle.activeFile.selection}\n\n`;
                }
            }

            // Project Structure
            if (contextBundle.workspace && contextBundle.workspace.fileStructure) {
                contextualMessage += 'Project Structure:\n';
                contextBundle.workspace.fileStructure.slice(0, 30).forEach((file: string) => {
                    contextualMessage += `- ${file}\n`;
                });
                if (contextBundle.workspace.fileStructure.length > 30) {
                    contextualMessage += `... and ${contextBundle.workspace.fileStructure.length - 30} more files\n`;
                }
            }

            contextualMessage += '[/CONTEXT]\n\n';
        }

        contextualMessage += '[INSTRUCTIONS]\n';
        contextualMessage += `The user wants to ${userMessage}.\n`;
        contextualMessage += 'CRITICAL: Your response must be a single, valid JSON object. Do not include any other text.\n';
        contextualMessage += 'Do NOT explain your reasoning. Do NOT say "Okay" or "I need to". Just return the JSON.\n';
        contextualMessage += 'Start your response with { and end with }. Nothing else.\n';
        contextualMessage += '[/INSTRUCTIONS]';

        return contextualMessage;
    }

    private parseAgentResponse(response: string): AgentResponse {
        const actions: AgentAction[] = [];
        const nextEdits: NextEdit[] = [];
        const sources: string[] = [];
        let content = response;

        try {
            // First try to parse the entire response as JSON
            const jsonResponse = JSON.parse(response.trim());
            return this.processJsonResponse(jsonResponse);
        } catch (error) {
            console.log('Full response is not JSON, attempting to extract JSON...');

            // Try to extract JSON from mixed content
            const jsonMatch = response.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                try {
                    const extractedJson = JSON.parse(jsonMatch[0]);
                    console.log('Successfully extracted JSON from mixed response');
                    return this.processJsonResponse(extractedJson);
                } catch (extractError) {
                    console.log('Extracted text is not valid JSON:', extractError);
                }
            }

            // If no valid JSON found, treat as chat response
            console.log('No valid JSON found, treating as chat response');
            content = response;
        }

        return {
            content,
            actions,
            nextEdits,
            sources
        };
    }

    private processJsonResponse(jsonResponse: any): AgentResponse {
        const actions: AgentAction[] = [];
        const nextEdits: NextEdit[] = [];
        const sources: string[] = [];
        let content = '';

        if (jsonResponse.action) {
            switch (jsonResponse.action) {
                case 'create_file':
                    actions.push({
                        type: 'create_file',
                        target: jsonResponse.file_path,
                        content: jsonResponse.content,
                        description: `Create file ${jsonResponse.file_path}`,
                        confidence: 0.95
                    });
                    content = `Creating file: ${jsonResponse.file_path}`;
                    break;

                case 'modify_file':
                    actions.push({
                        type: 'modify_file',
                        target: jsonResponse.file_path,
                        content: jsonResponse.content,
                        description: `Modify file ${jsonResponse.file_path}`,
                        confidence: 0.95
                    });
                    content = `Modifying file: ${jsonResponse.file_path}`;
                    break;

                case 'delete_file':
                    actions.push({
                        type: 'delete_file',
                        target: jsonResponse.file_path,
                        description: `Delete file ${jsonResponse.file_path}`,
                        confidence: 0.95
                    });
                    content = `Deleting file: ${jsonResponse.file_path}`;
                    break;

                case 'run_command':
                    actions.push({
                        type: 'run_command',
                        target: jsonResponse.command,
                        description: `Run command: ${jsonResponse.command}`,
                        confidence: 0.95
                    });
                    content = `Running command: ${jsonResponse.command}`;
                    break;

                case 'chat':
                    content = jsonResponse.message;
                    break;
            }
        }

        return {
            content,
            actions,
            nextEdits,
            sources
        };
    }

    async executeActions(actions: AgentAction[]): Promise<void> {
        for (const action of actions) {
            try {
                await this.toolRegistry.execute(action.type, action);
            } catch (error) {
                console.error(`Failed to execute action ${action.type}:`, error);
            }
        }
    }

    // Tool implementations
    private async createFile(action: AgentAction): Promise<void> {
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            throw new Error('No workspace folder open');
        }

        const filePath = vscode.Uri.joinPath(workspaceFolder.uri, action.target);
        const content = action.content || '';
        
        await vscode.workspace.fs.writeFile(filePath, Buffer.from(content, 'utf8'));
        
        // Open the created file
        const document = await vscode.workspace.openTextDocument(filePath);
        await vscode.window.showTextDocument(document);
    }

    private async modifyFile(action: AgentAction): Promise<void> {
        // Implementation for file modification
        console.log('Modifying file:', action.target);
    }

    private async deleteFile(action: AgentAction): Promise<void> {
        // Implementation for file deletion
        console.log('Deleting file:', action.target);
    }

    private async runCommand(action: AgentAction): Promise<void> {
        // Implementation for running terminal commands
        console.log('Running command:', action.target);
    }

    private async applyCode(action: AgentAction): Promise<void> {
        // Implementation for applying code to current location
        console.log('Applying code:', action.content);
    }

    private async getContext(action: AgentAction): Promise<any> {
        // Implementation for getting context
        return await this.contextEngine.gatherComprehensiveContext();
    }

    private getSelectedModel(): string {
        return vscode.workspace.getConfiguration('ollama-assistant').get('chatModel') || 'llama2';
    }

    private getOptimizedOptions(): any {
        return {
            temperature: 0.1,
            top_p: 0.9,
            num_ctx: 8192
        };
    }

    async dispose(): Promise<void> {
        await this.checkpoints.dispose();
        await this.memorySystem.dispose();
    }
}
