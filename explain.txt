Phase 1: The Context Engine (Information Gathering)
The AI needs to understand its environment. The Context Engine's job is to collect specific, relevant data points and package them into a structured format before they are sent to the Ollama model.

Instructions for the AI:

Get File Contents: Read the full text content of the user's currently active document.

Get Selected Code: If the user has a code block selected, read only that portion.

Get Project File Structure: Use VS Code's API to scan the workspace and generate a list of file and folder names. This gives the AI a map of the project. A simple way to do this is to recursively list file paths.

Combine and Format: Package all this information into a single text block with clear headings. The AI should embed this block into the main prompt.

Example of the prompt payload to be sent to Ollama:

[CONTEXT]
Active File:
/path/to/my-component.js
JavaScript

// Content of my-component.js
import React from 'react';
import { someHelper } from './utils';

// Selected code below:
function MyComponent() {
  return <div>{someHelper()}</div>;
}
Project Structure:
- src/
  - components/
  - services/
  - utils.js
  - my-component.js
- package.json
- README.md
[/CONTEXT]
The user wants to [USER PROMPT HERE]. Please respond with a JSON object. For file actions, use "action": "create_file" or "action": "modify_file". For simple chat, use "action": "chat".

Phase 2: The Agent's JSON Response
The key to the "Agent" functionality is that the AI's response is not a human-readable message. It's a command in a JSON format that the extension can parse and execute. The AI must be trained by the prompt to consistently produce this structure.

Instructions for the AI:

Action Command: Respond with a single JSON object. This object must contain an "action" key that specifies what to do.

File Actions: For tasks like creating, modifying, or deleting files, the JSON should contain the file_path and content (if applicable).

Chat Action: For general questions that don't require file system changes, use the "chat" action and include a "message" key with the response.

Example AI Responses:

To Create a File (create_file):

JSON

{
  "action": "create_file",
  "file_path": "src/utils/newHelper.js",
  "content": "export function newHelper() {\n  return 'Hello World';\n}"
}
To Modify a File (modify_file):

JSON

{
  "action": "modify_file",
  "file_path": "src/components/my-component.js",
  "content": "import React from 'react';\nimport { newHelper } from '../utils/newHelper';\n\nfunction MyComponent() {\n  return <div>{newHelper()}</div>;\n}"
}
To Delete a File (delete_file):

JSON

{
  "action": "delete_file",
  "file_path": "src/old-file.js"
}
For a Regular Chat Response (chat):

JSON

{
  "action": "chat",
  "message": "The `my-component.js` file you've selected is a simple React functional component that renders a div. I can add state to it if you'd like."
}
Phase 3: Extension Logic (Parsing & Execution)
This is the final step, where the extension's code takes the AI's JSON output and performs the requested action.

Instructions for the AI:

Receive Response: The extension's Node.js backend receives the JSON string from Ollama. It must parse it into a JavaScript object.

Parse the action: Use a switch statement or a series of if/else checks to determine the value of the action key.

Execute the Command: Based on the action, call the correct VS Code API function.

Pseudocode for the parsing logic:

const aiResponse = JSON.parse(responseFromOllama);

if (aiResponse.action === "create_file") {
    // SECURITY NOTE: ALWAYS ASK FOR USER CONFIRMATION!
    const confirmed = await vscode.window.showInformationMessage(
        `Augment Agent wants to create file: ${aiResponse.file_path}. Proceed?`,
        'Yes', 'No'
    );
    if (confirmed === 'Yes') {
        const fileUri = vscode.Uri.file(aiResponse.file_path);
        const encoder = new TextEncoder();
        const content = encoder.encode(aiResponse.content);
        await vscode.workspace.fs.writeFile(fileUri, content);
        vscode.window.showTextDocument(fileUri);
    }
}
else if (aiResponse.action === "modify_file") {
    // Call the VS Code API to find and modify the document
}
else if (aiResponse.action === "delete_file") {
    // Call the VS Code API to delete the file
}
else if (aiResponse.action === "chat") {
    // Display the message in the chat panel
}
This is the full pipeline. The AI's task is not to write code for the extension itself, but to produce a specific, well-defined JSON output that its human programmer (you) can easily integrate and act upon.