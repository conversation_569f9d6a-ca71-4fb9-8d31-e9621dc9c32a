{"name": "o<PERSON><PERSON>-code-assistant", "displayName": "Ollama Code Assistant", "description": "AI-powered coding assistant using local Ollama models", "version": "0.1.0", "engines": {"vscode": "^1.80.0"}, "categories": ["Other", "Machine Learning", "Snippets"], "keywords": ["ai", "ollama", "code-completion", "chat", "assistant"], "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "o<PERSON><PERSON>-assistant.openChat", "title": "Open Ollama Chat", "category": "Ollama Assistant"}, {"command": "o<PERSON><PERSON>-assistant.openTaskChat", "title": "Open AI Task Agent", "category": "Ollama Assistant"}, {"command": "o<PERSON><PERSON>-assistant.select<PERSON><PERSON><PERSON>", "title": "Select Ollama Model", "category": "Ollama Assistant"}, {"command": "o<PERSON><PERSON>-assistant.refresh<PERSON><PERSON><PERSON>", "title": "Refresh Available Models", "category": "Ollama Assistant"}, {"command": "olla<PERSON>-assistant.<PERSON><PERSON><PERSON><PERSON>", "title": "Clear Chat History", "category": "Ollama Assistant"}, {"command": "olla<PERSON>-assistant.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "Clear Task History", "category": "Ollama Assistant"}], "views": {"explorer": [{"id": "olla<PERSON>-assistant.chat<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "when": "true"}, {"id": "<PERSON><PERSON><PERSON>-assistant.task<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "AI Task Agent", "when": "true"}]}, "viewsWelcome": [{"view": "olla<PERSON>-assistant.chat<PERSON><PERSON><PERSON>", "contents": "Welcome to Ollama Code Assistant!\n[Open Chat](command:ollama-assistant.openChat)\n[Select Model](command:ollama-assistant.selectModel)"}, {"view": "<PERSON><PERSON><PERSON>-assistant.task<PERSON><PERSON><PERSON><PERSON><PERSON>", "contents": "AI Task Agent - Multi-step task execution\n[Open Task Agent](command:ollama-assistant.openTaskChat)\n[Select Model](command:ollama-assistant.selectModel)"}], "configuration": {"title": "Ollama Assistant", "properties": {"ollama-assistant.serverUrl": {"type": "string", "default": "http://localhost:11434", "description": "Ollama server URL"}, "ollama-assistant.defaultModel": {"type": "string", "default": "", "description": "Default model to use for completions"}, "ollama-assistant.chatModel": {"type": "string", "default": "", "description": "Model to use for chat conversations"}, "ollama-assistant.enableCompletions": {"type": "boolean", "default": true, "description": "Enable AI-powered code completions"}, "ollama-assistant.completionDelay": {"type": "number", "default": 500, "description": "Delay in milliseconds before triggering completions"}, "ollama-assistant.maxContextLines": {"type": "number", "default": 100, "description": "Maximum number of lines to include in context"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js", "package": "vsce package"}, "devDependencies": {"@types/vscode": "^1.80.0", "@types/node": "16.x", "@types/mocha": "^10.0.1", "@types/lodash": "^4.14.195", "@types/ws": "^8.5.5", "@types/glob": "^8.1.0", "@typescript-eslint/eslint-plugin": "^5.59.1", "@typescript-eslint/parser": "^5.59.1", "eslint": "^8.39.0", "typescript": "^5.0.4", "mocha": "^10.2.0", "glob": "^8.1.0", "@vscode/test-electron": "^2.3.0", "@vscode/vsce": "^2.19.0"}, "dependencies": {"axios": "^1.4.0", "ws": "^8.13.0", "lodash": "^4.17.21", "ignore": "^5.2.4"}}